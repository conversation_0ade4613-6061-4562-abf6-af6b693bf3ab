#!/usr/bin/env python3
"""
Simple test for PaddleOCR functionality
This tests just the OCR part without the heavy VLM model
"""

import os
import json
import time
from qwen2_paddle_extractor import PaddleOCRProcessor

def test_paddle_ocr():
    """Test PaddleOCR functionality"""
    
    image_path = "images/image_001.jpg"
    
    if not os.path.exists(image_path):
        print(f"❌ Test image not found: {image_path}")
        print("💡 Please place a test image at the specified path")
        return
    
    print("🚀 Testing PaddleOCR...")
    print(f"📁 Image: {image_path}")
    
    try:
        # Initialize PaddleOCR
        print("🔄 Initializing PaddleOCR...")
        ocr_processor = PaddleOCRProcessor()
        
        # Extract text
        print("🔄 Extracting text...")
        start_time = time.time()
        results = ocr_processor.extract_text(image_path, include_bbox=True)
        total_time = time.time() - start_time
        
        print(f"✅ OCR completed in {total_time:.2f} seconds")
        print(f"📊 Processing time: {results['processing_time']:.2f}s")
        print(f"📄 Status: {results['status']}")
        
        if results['text']:
            print(f"📝 Extracted text: {results['text']}")
            print(f"🔢 Number of text regions: {len(results['text_regions'])}")
            
            # Show first few text regions with bounding boxes
            if results['text_regions']:
                print("\n📍 Text regions (first 5):")
                for i, region in enumerate(results['text_regions'][:5]):
                    print(f"  {i+1}. '{region['text']}' (confidence: {region['confidence']:.3f})")
                    print(f"     bbox: {region['bbox']}")
        else:
            print("❌ No text found in the image")
        
        # Save results to file
        output_file = "paddle_ocr_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"💾 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error during OCR processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_paddle_ocr()
