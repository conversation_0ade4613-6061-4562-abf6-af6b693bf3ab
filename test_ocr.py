import requests

url = "http://localhost:8002/api/v1/sparrow-llm/inference"
files = {"file": open("images/image_001.jpg", "rb")}
# files = {"file": open("test.jpg", "rb")}
data = {
    "query": '[{"text":"str", "value":"str"}]',
    "pipeline": "sparrow-parse",
    "options": "huggingface,microsoft/Florence-2-base"
}

response = requests.post(url, files=files, data=data)
print(f"Status Code: {response.status_code}")
print(f"Response Headers: {response.headers}")
print(f"Response Text: {response.text}")

if response.status_code == 200:
    try:
        print("JSON Response:")
        print(response.json())
    except requests.exceptions.JSONDecodeError as e:
        print(f"Failed to decode JSON: {e}")
else:
    print(f"Request failed with status code: {response.status_code}")