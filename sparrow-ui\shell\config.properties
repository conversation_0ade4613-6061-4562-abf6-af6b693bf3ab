[settings]
backend_url = http://localhost:8002/api/v1/sparrow-llm/inference
backend_options_1 = mlx,mlx-community/Mistral-Small-3.1-24B-Instruct-2503-8bit,Standard model (reliable & versatile)
backend_options_2 = mlx,mlx-community/Qwen2.5-VL-72B-Instruct-4bit,Advanced model (optimized for complex documents)
version = 0.4.3
# Set to true to enable Oracle DB access
use_database = false
# Set to false to disable Sparrow Key validation (allows unrestricted access)
protected_access = false

[database]
# Oracle database connection settings
user = SCOTT
password = TIGER
host = 127.0.0.1
port = 1521
service = freepdb1