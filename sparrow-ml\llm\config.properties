[settings]
# LLM Model and API settings
llm_function = adrienbrault/nous-hermes2theta-llama3-8b:q5_K_M
ollama_base_url = http://127.0.0.1:11434/v1
protected_access = false
use_database = false

[keys]
# Sparrow API keys
key1_value = value1
key1_usage_count = 0
key1_usage_limit = 5
key2_value = value2
key2_usage_count = 0
key2_usage_limit = 3

[database]
# Oracle database connection settings (if needed)
user = SCOTT
password = TIGER
host = 127.0.0.1
port = 1521
service = freepdb1