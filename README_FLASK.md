# Sparrow Flask Application

This directory contains a Flask-based web application for the Sparrow document processing system. The Flask app provides the same functionality as the original FastAPI version but uses the Flask web framework.

## 🚀 Quick Start

### Windows
```bash
# Run the batch file (will handle virtual environment and dependencies)
run_flask.bat
```

### Linux/Mac
```bash
# Make the script executable and run
chmod +x run_flask.sh
./run_flask.sh
```

### Manual Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements_flask.txt

# Run the application
python flask_app.py
```

## 📋 Prerequisites

- Python 3.10+ 
- All Sparrow dependencies (sparrow-parse, etc.)
- For Vision LLM models: MLX (Mac) or appropriate GPU setup

## 🌐 API Endpoints

The Flask app provides the same API endpoints as the FastAPI version:

### Document Processing
```bash
POST /api/v1/sparrow-llm/inference
```

**Parameters:**
- `query` - JSON schema for data extraction
- `pipeline` - Processing pipeline (e.g., "sparrow-parse")  
- `options` - Model options (e.g., "mlx,mlx-community/Qwen2.5-VL-72B-Instruct-4bit")
- `file` - Document file to process
- `crop_size` - Optional crop size for table extraction
- `page_type` - Optional page type specification
- `debug` - Enable debug mode (true/false)

**Example:**
```bash
curl -X POST 'http://localhost:5000/api/v1/sparrow-llm/inference' \
  -F 'query=[{"field_name":"str", "amount":0}]' \
  -F 'pipeline=sparrow-parse' \
  -F 'options=mlx,mlx-community/Qwen2.5-VL-72B-Instruct-4bit' \
  -F 'file=@document.pdf'
```

### Text-Only Processing
```bash
POST /api/v1/sparrow-llm/instruction-inference
```

**Parameters:**
- `query` - Text instruction or question
- `pipeline` - Processing pipeline
- `options` - Model options

**Example:**
```bash
curl -X POST 'http://localhost:5000/api/v1/sparrow-llm/instruction-inference' \
  -F 'query=What is the capital of France?' \
  -F 'pipeline=sparrow-instructor' \
  -F 'options=mlx,mlx-community/Mistral-Small-3.1-24B-Instruct-2503-8bit'
```

### Health Check
```bash
GET /health
```

## 🔧 Configuration

The Flask app uses the same configuration as the original Sparrow setup:

1. **Environment Variables**: Create a `.env` file in the `sparrow-ml/llm/` directory
2. **Config File**: Uses `sparrow-ml/llm/config.properties`
3. **Database**: Optional database integration for API key management

### Example .env file:
```bash
HF_TOKEN=your_huggingface_token_here
```

### Example config.properties:
```ini
[settings]
protected_access = false
use_database = false
sparrow_keys = key1,key2,key3
```

## 🎯 Usage Examples

### Invoice Processing
```bash
curl -X POST 'http://localhost:5000/api/v1/sparrow-llm/inference' \
  -F 'query=[{"invoice_number":"str", "date":"str", "total_amount":0}]' \
  -F 'pipeline=sparrow-parse' \
  -F 'options=mlx,mlx-community/Qwen2.5-VL-72B-Instruct-4bit' \
  -F 'file=@invoice.pdf'
```

### Table Extraction
```bash
curl -X POST 'http://localhost:5000/api/v1/sparrow-llm/inference' \
  -F 'query=[{"instrument_name":"str", "valuation":0}]' \
  -F 'pipeline=sparrow-parse' \
  -F 'options=mlx,mlx-community/Qwen2.5-VL-72B-Instruct-4bit' \
  -F 'file=@table.png'
```

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're running from the sparrow-main directory
2. **Missing Dependencies**: Install requirements with `pip install -r requirements_flask.txt`
3. **Port Already in Use**: Change port with `python flask_app.py --port 5001`
4. **File Upload Issues**: Check file size (max 16MB) and allowed extensions

### Debug Mode
```bash
python flask_app.py --debug
```

### Custom Port
```bash
python flask_app.py --port 8080
```

### Custom Host
```bash
python flask_app.py --host 127.0.0.1 --port 8080
```

## 📊 Differences from FastAPI Version

- **Framework**: Uses Flask instead of FastAPI
- **Documentation**: Built-in HTML documentation at root URL
- **Async Handling**: Properly handles async operations within Flask context
- **CORS**: Enabled for all routes using Flask-CORS
- **Error Handling**: Flask-style error handling with proper HTTP status codes

## 🔗 Integration

The Flask app can be used as a drop-in replacement for the FastAPI version. All API endpoints maintain the same interface and functionality.

## 📝 Development

To extend the Flask app:

1. Add new routes in `flask_app.py`
2. Update the HTML documentation in the home route
3. Add any new dependencies to `requirements_flask.txt`
4. Test with both file uploads and text-only requests

## 🚀 Production Deployment

For production deployment, consider using:

- **Gunicorn**: `gunicorn -w 4 -b 0.0.0.0:5000 flask_app:app`
- **uWSGI**: `uwsgi --http :5000 --module flask_app:app`
- **Docker**: Create a Dockerfile based on the existing Sparrow Docker setup

## 📄 License

Same license as the main Sparrow project.
