#!/usr/bin/env python3
"""
Test OCR using local Sparrow Parse without MLX dependency
This version works on Windows by using local PyTorch models
"""

import json
from sparrow_parse.extractors.vllm_extractor import VLLMExtractor

def test_local_ocr():
    """Test OCR using local processing"""
    
    # Initialize the extractor
    extractor = VLLMExtractor()
    
    # Define the query schema
    query_schema = [{"text": "str", "value": "str"}]
    
    # Image path
    image_path = "images/image_001.jpg"
    
    try:
        print("Processing image with local OCR...")
        
        # Process the image
        result = extractor.extract(
            file_path=image_path,
            query=json.dumps(query_schema),
            options=["local"]  # Use local processing instead of MLX
        )
        
        print("OCR Result:")
        print(json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Error during OCR processing: {e}")
        print("This might be due to missing models or dependencies.")
        print("Try using the API approach instead.")

if __name__ == "__main__":
    test_local_ocr()
