"""
Mock MLX VLM module for Windows compatibility
This provides dummy implementations to prevent import errors
"""

def load(*args, **kwargs):
    """Mock load function"""
    raise NotImplementedError("MLX is not available on Windows. Use a different backend.")

def generate(*args, **kwargs):
    """Mock generate function"""
    raise NotImplementedError("MLX is not available on Windows. Use a different backend.")

# Add any other functions that might be imported
__all__ = ['load', 'generate']
