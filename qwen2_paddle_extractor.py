#!/usr/bin/env python3
"""
Custom extractor combining Qwen2-VL-72B and PaddleOCR
This script provides a complete solution for OCR and VLM processing on Windows
"""

import json
import os
import tempfile
import shutil
from typing import List, Dict, Any, Optional, Tuple
from PIL import Image
import torch
from transformers import Qwen2VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info
from paddleocr import PaddleOCR
from rich import print
import time


class PaddleOCRProcessor:
    """PaddleOCR integration for text extraction"""
    
    def __init__(self):
        """Initialize PaddleOCR"""
        self.ocr = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False,
            lang='en'  # Set language to English
        )
        print("✓ PaddleOCR initialized")
    
    def extract_text(self, image_path: str, include_bbox: bool = True) -> Dict[str, Any]:
        """
        Extract text from image using PaddleOCR
        
        Args:
            image_path: Path to the image file
            include_bbox: Whether to include bounding box coordinates
            
        Returns:
            Dictionary containing extracted text and optional bounding boxes
        """
        try:
            start_time = time.time()
            result = self.ocr.ocr(image_path, cls=True)
            processing_time = time.time() - start_time
            
            if not result or not result[0]:
                return {
                    "text": "",
                    "text_regions": [],
                    "processing_time": processing_time,
                    "status": "no_text_found"
                }
            
            # Extract text and bounding boxes
            text_parts = []
            text_regions = []
            
            for line in result[0]:
                bbox, (text, confidence) = line
                text_parts.append(text)
                
                if include_bbox:
                    text_regions.append({
                        "text": text,
                        "confidence": confidence,
                        "bbox": bbox
                    })
            
            full_text = " ".join(text_parts)
            
            return {
                "text": full_text,
                "text_regions": text_regions if include_bbox else [],
                "processing_time": processing_time,
                "status": "success"
            }
            
        except Exception as e:
            print(f"❌ PaddleOCR error: {e}")
            return {
                "text": "",
                "text_regions": [],
                "processing_time": 0,
                "status": f"error: {str(e)}"
            }


class Qwen2VLProcessor:
    """Qwen2-VL-72B integration for vision-language processing"""
    
    def __init__(self, model_name: str = "Qwen/Qwen2-VL-7B-Instruct"):
        """
        Initialize Qwen2-VL model
        
        Args:
            model_name: Hugging Face model name
        """
        self.model_name = model_name
        self.model = None
        self.processor = None
        self._load_model()
    
    def _load_model(self):
        """Load the Qwen2-VL model and processor"""
        try:
            print(f"🔄 Loading Qwen2-VL model: {self.model_name}")
            
            # Load model with appropriate settings for different hardware
            device_map = "auto" if torch.cuda.is_available() else "cpu"
            torch_dtype = "auto" if torch.cuda.is_available() else torch.float32
            
            self.model = Qwen2VLForConditionalGeneration.from_pretrained(
                self.model_name,
                torch_dtype=torch_dtype,
                device_map=device_map,
                trust_remote_code=True
            )
            
            self.processor = AutoProcessor.from_pretrained(
                self.model_name,
                trust_remote_code=True
            )
            
            print("✓ Qwen2-VL model loaded successfully")
            
        except Exception as e:
            print(f"❌ Failed to load Qwen2-VL model: {e}")
            print("💡 Tip: Make sure you have sufficient GPU memory or try a smaller model")
            raise
    
    def process_image_with_query(self, image_path: str, query: str) -> str:
        """
        Process image with text query using Qwen2-VL
        
        Args:
            image_path: Path to the image file
            query: Text query for the model
            
        Returns:
            Model response as string
        """
        try:
            # Prepare the conversation
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path,
                        },
                        {"type": "text", "text": query},
                    ],
                }
            ]
            
            # Prepare inputs
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs = process_vision_info(messages)
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            
            # Move inputs to the same device as model
            if torch.cuda.is_available():
                inputs = inputs.to("cuda")
            
            # Generate response
            generated_ids = self.model.generate(**inputs, max_new_tokens=2048)
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )
            
            return output_text[0] if output_text else ""
            
        except Exception as e:
            print(f"❌ Qwen2-VL processing error: {e}")
            return f"Error: {str(e)}"


class Qwen2PaddleExtractor:
    """
    Combined extractor using both PaddleOCR and Qwen2-VL-72B
    Similar interface to VLLMExtractor but with enhanced capabilities
    """
    
    def __init__(self, qwen_model_name: str = "Qwen/Qwen2-VL-7B-Instruct"):
        """
        Initialize the combined extractor
        
        Args:
            qwen_model_name: Qwen2-VL model name
        """
        self.paddle_ocr = PaddleOCRProcessor()
        self.qwen_vl = Qwen2VLProcessor(qwen_model_name)
        print("✓ Qwen2PaddleExtractor initialized")
    
    def extract(self, 
                file_path: str, 
                query: str, 
                use_ocr: bool = True, 
                use_vllm: bool = True,
                include_bbox: bool = False,
                debug: bool = False) -> Dict[str, Any]:
        """
        Extract data from image using OCR and/or VLM
        
        Args:
            file_path: Path to the image file
            query: Query for data extraction
            use_ocr: Whether to use PaddleOCR
            use_vllm: Whether to use Qwen2-VL
            include_bbox: Whether to include bounding boxes from OCR
            debug: Enable debug output
            
        Returns:
            Dictionary containing extraction results
        """
        if not os.path.exists(file_path):
            return {"error": f"File not found: {file_path}"}
        
        results = {
            "file_path": file_path,
            "query": query,
            "timestamp": time.time()
        }
        
        if debug:
            print(f"🔄 Processing: {file_path}")
            print(f"📝 Query: {query}")
        
        # PaddleOCR extraction
        if use_ocr:
            if debug:
                print("🔄 Running PaddleOCR...")
            
            ocr_results = self.paddle_ocr.extract_text(file_path, include_bbox)
            results["ocr"] = ocr_results
            
            if debug:
                print(f"✓ OCR completed in {ocr_results['processing_time']:.2f}s")
                print(f"📄 Extracted text: {ocr_results['text'][:100]}...")
        
        # Qwen2-VL processing
        if use_vllm:
            if debug:
                print("🔄 Running Qwen2-VL...")
            
            start_time = time.time()
            vllm_response = self.qwen_vl.process_image_with_query(file_path, query)
            vllm_time = time.time() - start_time
            
            results["vllm"] = {
                "response": vllm_response,
                "processing_time": vllm_time,
                "model": self.qwen_vl.model_name
            }
            
            if debug:
                print(f"✓ VLM completed in {vllm_time:.2f}s")
                print(f"🤖 VLM response: {vllm_response[:200]}...")
        
        return results
    
    def extract_structured_data(self, 
                               file_path: str, 
                               schema: List[Dict[str, str]], 
                               debug: bool = False) -> Dict[str, Any]:
        """
        Extract structured data according to a schema
        
        Args:
            file_path: Path to the image file
            schema: List of field definitions (e.g., [{"field_name": "str", "amount": 0}])
            debug: Enable debug output
            
        Returns:
            Dictionary containing structured extraction results
        """
        # Convert schema to query
        schema_str = json.dumps(schema, indent=2)
        query = f"Extract the following data from this document and return it in JSON format: {schema_str}"
        
        if debug:
            print(f"📋 Schema: {schema_str}")
        
        # Extract using VLM (more suitable for structured data)
        results = self.extract(
            file_path=file_path,
            query=query,
            use_ocr=True,  # Include OCR for reference
            use_vllm=True,
            debug=debug
        )
        
        # Try to parse VLM response as JSON
        if "vllm" in results:
            try:
                vllm_response = results["vllm"]["response"]
                # Try to extract JSON from the response
                import re
                json_match = re.search(r'\{.*\}', vllm_response, re.DOTALL)
                if json_match:
                    parsed_data = json.loads(json_match.group())
                    results["structured_data"] = parsed_data
                    results["extraction_status"] = "success"
                else:
                    results["structured_data"] = {"raw_response": vllm_response}
                    results["extraction_status"] = "no_json_found"
            except json.JSONDecodeError as e:
                results["structured_data"] = {"error": f"JSON parsing failed: {e}"}
                results["extraction_status"] = "json_parse_error"
        
        return results


def main():
    """Example usage of the Qwen2PaddleExtractor"""
    print("🚀 Qwen2-VL + PaddleOCR Extractor Demo")
    
    # Initialize extractor
    try:
        extractor = Qwen2PaddleExtractor()
    except Exception as e:
        print(f"❌ Failed to initialize extractor: {e}")
        return
    
    # Example usage
    image_path = "images/image_001.jpg"
    
    if not os.path.exists(image_path):
        print(f"❌ Test image not found: {image_path}")
        print("💡 Please place a test image at the specified path")
        return
    
    # Test 1: Basic extraction
    print("\n📋 Test 1: Basic OCR + VLM extraction")
    results = extractor.extract(
        file_path=image_path,
        query="What text and information can you see in this image?",
        debug=True
    )
    
    print("\n📊 Results:")
    print(json.dumps(results, indent=2, default=str))
    
    # Test 2: Structured data extraction
    print("\n📋 Test 2: Structured data extraction")
    schema = [{"text": "str", "value": "str", "amount": 0}]
    structured_results = extractor.extract_structured_data(
        file_path=image_path,
        schema=schema,
        debug=True
    )
    
    print("\n📊 Structured Results:")
    print(json.dumps(structured_results, indent=2, default=str))


if __name__ == "__main__":
    main()
