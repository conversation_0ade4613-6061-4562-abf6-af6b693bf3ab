#!/usr/bin/env python3
"""
Simple runner script for Sparrow Flask application.
This script handles the async function calls properly for Flask.
"""

import asyncio
import sys
import os
from flask_app import app, main

# Ensure we can import the sparrow modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'sparrow-ml', 'llm'))

def run_flask_app():
    """Run the Flask application with proper async handling."""
    # Set up event loop for async operations
    if sys.platform.startswith('win'):
        # On Windows, use Proactor<PERSON>ventLoop for better compatibility
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Create new event loop for this thread
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Run the main Flask app
        main()
    finally:
        loop.close()

if __name__ == '__main__':
    run_flask_app()
