#!/usr/bin/env python3
"""
Direct OCR test using Sparrow Parse without API
This bypasses the API and uses the library directly
"""

import sys
import os
import json
import tempfile

# Add the sparrow-ml/llm directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'sparrow-ml', 'llm'))

try:
    from engine import run_from_api_engine
    print("✓ Engine imported successfully")
except ImportError as e:
    print(f"✗ Failed to import engine: {e}")
    sys.exit(1)

class MockFile:
    """Mock file object for testing"""
    def __init__(self, file_path):
        self.filename = os.path.basename(file_path)
        self.file_path = file_path
    
    async def read(self):
        with open(self.file_path, 'rb') as f:
            return f.read()

async def test_direct_ocr():
    """Test OCR using direct engine call"""
    
    # Configuration
    image_path = "images/image_001.jpg"
    query = '[{"text":"str", "value":"str"}]'
    pipeline = "sparrow-parse"
    
    # Try different options that might work on Windows
    options_to_try = [
        ["huggingface", "microsoft/Florence-2-base"],
        ["local"],
        ["cpu"],
    ]
    
    if not os.path.exists(image_path):
        print(f"✗ Image file not found: {image_path}")
        return
    
    print(f"✓ Image file found: {image_path}")
    
    for options in options_to_try:
        print(f"\n🔄 Trying options: {options}")
        
        try:
            # Create mock file object
            mock_file = MockFile(image_path)
            
            # Call the engine directly
            result = await run_from_api_engine(
                user_selected_pipeline=pipeline,
                query=query,
                options_arr=options,
                crop_size=None,
                page_type=None,
                file=mock_file,
                debug_dir=None,
                debug=True
            )
            
            print("✓ OCR processing successful!")
            print("Result:")
            print(json.dumps(result, indent=2))
            return  # Success, exit
            
        except Exception as e:
            print(f"✗ Failed with options {options}: {e}")
            continue
    
    print("\n❌ All options failed. The issue might be:")
    print("1. Missing model dependencies")
    print("2. Incompatible backend for Windows")
    print("3. Network issues (for Hugging Face models)")

def main():
    """Main function"""
    print("🚀 Starting direct OCR test...")
    
    # Run the async test
    import asyncio
    try:
        asyncio.run(test_direct_ocr())
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
